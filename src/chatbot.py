"""
Main MCP Chatbot implementation.
"""

import asyncio
from typing import Dict, Any, Optional, AsyncGenerator
from loguru import logger

from .config import Config, get_config
from .mcp_client import MCPClientManager


class MCPChatbot:
    """Main chatbot class with MCP capabilities."""

    def __init__(self, config: Optional[Config] = None):
        self.config = config or get_config()

        # Initialize components
        self.mcp_manager = MCPClientManager(config=self.config)

        # Conversation history
        self.conversation_history = []

        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize all chatbot components."""
        if self._initialized:
            return

        logger.info("🚀 Initializing MCP Chatbot...")

        try:
            # Initialize MCP manager
            await self.mcp_manager.initialize()

            self._initialized = True
            logger.info("✅ MCP Chatbot initialized successfully!")

        except Exception as e:
            logger.error(f"❌ Failed to initialize chatbot: {e}")
            raise
    

    
    async def chat(self, query: str) -> str:
        """Process chat query using MCP agent."""
        if not self._initialized:
            await self.initialize()

        logger.info(f"💬 Processing query: {query[:100]}...")

        try:
            # Process with MCP agent
            logger.info("🤖 Processing with MCP agent...")
            response = await self.mcp_manager.chat(query)

            # Store in conversation history
            self.conversation_history.append({
                'query': query,
                'response': response,
                'timestamp': asyncio.get_event_loop().time()
            })

            # Limit conversation history
            if len(self.conversation_history) > self.config.max_conversation_history:
                self.conversation_history = self.conversation_history[-self.config.max_conversation_history:]

            logger.info("✅ Query processed successfully")
            return response

        except Exception as e:
            error_msg = f"❌ Error processing query: {str(e)}"
            logger.error(error_msg)
            return error_msg
    
    async def stream_chat(self, query: str) -> AsyncGenerator[str, None]:
        """Process chat query with streaming response."""
        if not self._initialized:
            await self.initialize()

        logger.info(f"🔄 Streaming query: {query[:100]}...")

        try:
            # Stream response from MCP agent
            full_response = ""
            async for chunk in self.mcp_manager.stream_chat(query):
                full_response += chunk
                yield chunk

            # Store in conversation history
            self.conversation_history.append({
                'query': query,
                'response': full_response,
                'timestamp': asyncio.get_event_loop().time()
            })

        except Exception as e:
            error_msg = f"❌ Streaming error: {str(e)}"
            logger.error(error_msg)
            yield error_msg
    
    async def get_system_info(self) -> Dict[str, Any]:
        """Get system information and status."""
        if not self._initialized:
            await self.initialize()

        try:
            # Get MCP connection status
            mcp_status = await self.mcp_manager.test_connection()

            return {
                "chatbot_initialized": self._initialized,
                "conversation_count": len(self.conversation_history),
                "mcp_status": mcp_status,
                "config": {
                    "llm_provider": self.config.default_llm_provider,
                    "llm_model": self.config.default_llm_model,
                    "max_conversation_history": self.config.max_conversation_history
                }
            }

        except Exception as e:
            logger.error(f"❌ Error getting system info: {e}")
            return {"error": str(e)}
    
    async def list_available_tools(self) -> Dict[str, Any]:
        """List available MCP tools."""
        if not self._initialized:
            await self.initialize()
        
        return await self.mcp_manager.list_tools()
    
    def clear_conversation_history(self) -> None:
        """Clear conversation history."""
        self.conversation_history.clear()
        logger.info("🗑️ Conversation history cleared")
    

    
    async def cleanup(self) -> None:
        """Clean up all resources."""
        logger.info("🧹 Cleaning up RAG MCP Chatbot...")
        
        try:
            # Cleanup MCP manager
            await self.mcp_manager.cleanup()
            
            # Cleanup RAG system
            self.rag_system.cleanup()
            
            # Clear conversation history
            self.conversation_history.clear()
            
            self._initialized = False
            logger.info("✅ Chatbot cleanup completed")
            
        except Exception as e:
            logger.error(f"⚠️ Error during cleanup: {e}")
    
    def __del__(self):
        """Destructor to ensure cleanup."""
        if self._initialized:
            try:
                asyncio.create_task(self.cleanup())
            except:
                pass
