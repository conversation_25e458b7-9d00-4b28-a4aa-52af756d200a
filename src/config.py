"""
Configuration management for RAG MCP Chatbot.
"""

import os
import json
from typing import Dict, Any, Optional
from pathlib import Path
from pydantic import BaseModel, Field
from pydantic_settings import BaseSettings


class MCPServerConfig(BaseModel):
    """Configuration for a single MCP server."""
    url: str
    name: str
    description: str = ""
    timeout: int = 30
    retry_attempts: int = 3
    enabled: bool = True


class MCPConfig(BaseModel):
    """MCP configuration container."""
    mcpServers: Dict[str, MCPServerConfig]
    default_server: str
    global_settings: Dict[str, Any] = {}


class Config(BaseSettings):
    """Main application configuration."""
    
    # LLM Configuration
    openai_api_key: Optional[str] = Field(None, env="OPENAI_API_KEY")
    anthropic_api_key: Optional[str] = Field(None, env="ANTHROPIC_API_KEY")
    
    # MCP Server Configuration
    mcp_server_url: str = Field("http://localhost:8000/mcp/", env="MCP_SERVER_URL")
    mcp_server_health_url: str = Field("http://localhost:8000/health", env="MCP_SERVER_HEALTH_URL")
    mcp_server_name: str = Field("default", env="MCP_SERVER_NAME")
    
    # RAG Configuration
    vector_db_type: str = Field("chroma", env="VECTOR_DB_TYPE")
    vector_db_path: str = Field("./data/vectordb", env="VECTOR_DB_PATH")
    embedding_model: str = Field("text-embedding-3-small", env="EMBEDDING_MODEL")
    chunk_size: int = Field(1000, env="CHUNK_SIZE")
    chunk_overlap: int = Field(200, env="CHUNK_OVERLAP")
    
    # LLM Settings
    default_llm_provider: str = Field("openai", env="DEFAULT_LLM_PROVIDER")
    default_llm_model: str = Field("gpt-4o", env="DEFAULT_LLM_MODEL")
    llm_temperature: float = Field(0.1, env="LLM_TEMPERATURE")
    max_tokens: int = Field(2000, env="MAX_TOKENS")
    
    # Application Settings
    log_level: str = Field("INFO", env="LOG_LEVEL")
    debug: bool = Field(False, env="DEBUG")
    max_conversation_history: int = Field(50, env="MAX_CONVERSATION_HISTORY")
    
    # Web Interface
    streamlit_port: int = Field(8501, env="STREAMLIT_PORT")
    api_port: int = Field(8080, env="API_PORT")
    
    # Performance
    max_concurrent_requests: int = Field(10, env="MAX_CONCURRENT_REQUESTS")
    request_timeout: int = Field(30, env="REQUEST_TIMEOUT")
    retry_attempts: int = Field(3, env="RETRY_ATTEMPTS")
    
    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"
        case_sensitive = False


def load_mcp_config(config_path: str = "config/mcp_config.json") -> MCPConfig:
    """Load MCP configuration from JSON file."""
    config_file = Path(config_path)
    
    if not config_file.exists():
        raise FileNotFoundError(f"MCP config file not found: {config_path}")
    
    with open(config_file, 'r', encoding='utf-8') as f:
        data = json.load(f)
    
    # Convert server configs to MCPServerConfig objects
    servers = {}
    for name, server_data in data["mcpServers"].items():
        servers[name] = MCPServerConfig(**server_data)
    
    return MCPConfig(
        mcpServers=servers,
        default_server=data["default_server"],
        global_settings=data.get("global_settings", {})
    )


def get_config() -> Config:
    """Get application configuration."""
    return Config()


def get_mcp_config() -> MCPConfig:
    """Get MCP configuration."""
    return load_mcp_config()


# Global config instances
config = get_config()
mcp_config = get_mcp_config()
