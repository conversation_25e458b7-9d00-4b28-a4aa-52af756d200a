"""
MCP Client Manager using mcp-use library.
"""

import asyncio
from typing import Dict, Any, Optional, List
from loguru import logger
from mcp_use import MCPAgent, MCPClient
from langchain_openai import ChatOpenAI
from langchain_anthropic import ChatAnthropic

from .config import Config, MCPConfig, get_config, get_mcp_config


class MCPClientManager:
    """Manager for MCP clients and agents."""
    
    def __init__(self, config: Optional[Config] = None, mcp_config: Optional[MCPConfig] = None):
        self.config = config or get_config()
        self.mcp_config = mcp_config or get_mcp_config()
        
        self.client: Optional[MCPClient] = None
        self.agent: Optional[MCPAgent] = None
        self.llm = None
        
        self._initialized = False
    
    async def initialize(self) -> None:
        """Initialize MCP client and agent."""
        if self._initialized:
            return
        
        logger.info("🔧 Initializing MCP client manager...")
        
        try:
            # Create MCP client configuration
            client_config = self._build_client_config()
            
            # Initialize MCP client
            logger.info("📡 Creating MCP client...")
            self.client = MCPClient.from_dict(client_config)
            
            # Initialize LLM
            logger.info("🤖 Initializing LLM...")
            self.llm = self._create_llm()
            
            # Create MCP agent
            logger.info("🚀 Creating MCP agent...")
            self.agent = MCPAgent(
                llm=self.llm,
                client=self.client,
                max_steps=self.mcp_config.global_settings.get("max_steps", 15),
                verbose=self.mcp_config.global_settings.get("verbose", True),
                use_server_manager=False,  # Disable to avoid fastembed dependency
                disallowed_tools=self.mcp_config.global_settings.get("disallowed_tools", [])
            )
            
            self._initialized = True
            logger.info("✅ MCP client manager initialized successfully!")
            
        except Exception as e:
            logger.error(f"❌ Failed to initialize MCP client manager: {e}")
            raise
    
    def _build_client_config(self) -> Dict[str, Any]:
        """Build mcp-use client configuration."""
        client_config = {"mcpServers": {}}
        
        # Add enabled servers
        for name, server in self.mcp_config.mcpServers.items():
            if server.enabled:
                client_config["mcpServers"][name] = {
                    "url": server.url
                }
                logger.info(f"📍 Added MCP server: {name} -> {server.url}")
        
        return client_config
    
    def _create_llm(self):
        """Create LLM instance based on configuration."""
        provider = self.config.default_llm_provider.lower()
        model = self.config.default_llm_model
        temperature = self.config.llm_temperature
        
        if provider == "openai":
            if not self.config.openai_api_key:
                raise ValueError("OpenAI API key not configured")
            
            return ChatOpenAI(
                model=model,
                temperature=temperature,
                max_tokens=self.config.max_tokens,
                api_key=self.config.openai_api_key
            )
        
        elif provider == "anthropic":
            if not self.config.anthropic_api_key:
                raise ValueError("Anthropic API key not configured")
            
            return ChatAnthropic(
                model=model,
                temperature=temperature,
                max_tokens=self.config.max_tokens,
                api_key=self.config.anthropic_api_key
            )
        
        else:
            raise ValueError(f"Unsupported LLM provider: {provider}")
    
    async def chat(self, query: str) -> str:
        """Process chat query using MCP agent."""
        if not self._initialized:
            await self.initialize()
        
        try:
            logger.info(f"💬 Processing query: {query[:100]}...")
            result = await self.agent.run(query)
            logger.info("✅ Query processed successfully")
            return result
            
        except Exception as e:
            logger.error(f"❌ Error processing query: {e}")
            raise
    
    async def stream_chat(self, query: str):
        """Process chat query with streaming response."""
        if not self._initialized:
            await self.initialize()

        try:
            logger.info(f"🔄 Streaming query: {query[:100]}...")

            # mcp-use MCPAgent doesn't have astream method
            # Fall back to regular chat and yield the full response
            result = await self.agent.run(query)

            # Simulate streaming by yielding chunks
            chunk_size = 50  # Characters per chunk
            for i in range(0, len(result), chunk_size):
                chunk = result[i:i + chunk_size]
                yield chunk
                # Small delay to simulate streaming
                await asyncio.sleep(0.05)

        except Exception as e:
            logger.error(f"❌ Error streaming query: {e}")
            yield f"Error: {str(e)}"
    
    async def list_tools(self) -> List[Dict[str, Any]]:
        """List available tools from MCP servers."""
        if not self._initialized:
            await self.initialize()
        
        try:
            # Use agent to get tool information
            result = await self.agent.run("List all available tools with their descriptions")
            return {"tools_info": result}
            
        except Exception as e:
            logger.error(f"❌ Error listing tools: {e}")
            return {"error": str(e)}
    
    async def test_connection(self) -> Dict[str, Any]:
        """Test MCP server connections."""
        if not self._initialized:
            await self.initialize()
        
        results = {}
        
        for name, server in self.mcp_config.mcpServers.items():
            if not server.enabled:
                results[name] = {"status": "disabled"}
                continue
            
            try:
                # Test basic connectivity
                test_query = f"Test connection to {name} server"
                result = await self.agent.run(test_query)
                
                results[name] = {
                    "status": "connected",
                    "url": server.url,
                    "test_result": result[:200] + "..." if len(result) > 200 else result
                }
                
            except Exception as e:
                results[name] = {
                    "status": "error",
                    "url": server.url,
                    "error": str(e)
                }
        
        return results
    
    async def cleanup(self) -> None:
        """Clean up resources."""
        if self.client:
            try:
                await self.client.close_all_sessions()
                logger.info("🧹 MCP client sessions closed")
            except Exception as e:
                logger.warning(f"⚠️ Error closing MCP sessions: {e}")
        
        self._initialized = False
        logger.info("✅ MCP client manager cleanup completed")
    
    def __del__(self):
        """Destructor to ensure cleanup."""
        if self._initialized and self.client:
            try:
                asyncio.create_task(self.cleanup())
            except:
                pass
