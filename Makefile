# RAG MCP Chatbot - Makefile

.PHONY: help install setup test chat clean

# Default target
help:
	@echo "🤖 RAG MCP Chatbot - Available Commands"
	@echo "======================================"
	@echo ""
	@echo "Setup:"
	@echo "  install     - Install dependencies"
	@echo "  setup       - Setup environment and config"
	@echo ""
	@echo "Testing:"
	@echo "  test        - Test basic functionality"
	@echo ""
	@echo "Running:"
	@echo "  chat        - Interactive chat mode"
	@echo ""
	@echo "Development:"
	@echo "  clean       - Clean up generated files"

# Setup commands
install:
	@echo "📦 Installing dependencies..."
	pip install -r requirements.txt

setup: install
	@echo "⚙️ Setting up environment..."
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "📝 Created .env file from template"; \
		echo "⚠️  Please edit .env with your API keys"; \
	else \
		echo "✅ .env file already exists"; \
	fi
	@mkdir -p data/vectordb
	@echo "✅ Setup complete!"

# Testing commands
test:
	@echo "🧪 Running basic test..."
	python test.py

# Running commands
chat:
	@echo "💬 Starting interactive chat..."
	python main.py interactive

# Development commands
clean:
	@echo "🧹 Cleaning up..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type f -name "*.pyo" -delete 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	@echo "✅ Cleanup complete"
