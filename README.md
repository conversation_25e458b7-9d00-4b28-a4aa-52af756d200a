# 🤖 RAG MCP Chatbot

A simple RAG chatbot that integrates with MCP servers using **mcp-use** library.

## 🎯 Features

- ✅ **mcp-use Integration**: Connect to any MCP server via HTTP
- ✅ **RAG System**: Vector-based knowledge retrieval
- ✅ **Smart Routing**: Automatic decision between RAG and MCP tools
- ✅ **Interactive CLI**: Command-line chat interface

## 🏗️ Architecture

```
User Query → RAG Chatbot → mcp-use → MCP Server → External APIs
     ↓              ↓
Vector Search   Tool Calling
+ Knowledge    + Function
  Base          Execution
```

## 📦 Quick Start

### 1. Installation

```bash
git clone <this-repo>
cd rag-mcp-chatbot
pip install -r requirements.txt
```

### 2. Configuration

```bash
cp .env.example .env
# Edit .env with your API keys
```

### 3. Run

```bash
# Interactive chat
python main.py interactive

# Or use make
make chat
```

## 🔧 Configuration

Configure your MCP servers in `config/mcp_config.json`:

```json
{
  "mcpServers": {
    "your-server": {
      "url": "http://localhost:8000/mcp/"
    }
  }
}
```

## 📚 Usage

Simply run the interactive chat:

```bash
python main.py interactive
```

Available commands in chat:
- `/help` - Show help
- `/info` - Show system info
- `/tools` - List available MCP tools
- `/clear` - Clear conversation history
- `/quit` - Exit

## 🔧 Configuration

Configure your MCP servers in `config/mcp_config.json`:

```json
{
  "mcpServers": {
    "your-server": {
      "url": "http://localhost:8000/mcp/"
    }
  }
}
```

## 📄 License

MIT License
